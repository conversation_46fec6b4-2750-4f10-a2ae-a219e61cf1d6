import { gql } from '@apollo/client'
import { GET_TIMELINE_CASE_DATA } from './queries'

describe('Date-aware room status filtering queries', () => {
  it('should include date parameters in rooms resolver', () => {
    const query = GET_TIMELINE_CASE_DATA
    const queryString = query.loc?.source.body || ''

    expect(queryString).toContain('rooms(roomIds: $roomIds, statusFilter: $statusFilter, minEndTime: $minEndTime, maxStartTime: $maxStartTime)')
  })

  it('should include date parameters in room status field', () => {
    const query = GET_TIMELINE_CASE_DATA
    const queryString = query.loc?.source.body || ''

    expect(queryString).toContain('status(minEndTime: $minEndTime, maxStartTime: $maxStartTime)')
  })

  it('should have consistent date parameter names', () => {
    const query = GET_TIMELINE_CASE_DATA
    const queryString = query.loc?.source.body || ''

    const minEndTimeCount = (queryString.match(/minEndTime: \$minEndTime/g) || []).length
    const maxStartTimeCount = (queryString.match(/maxStartTime: \$maxStartTime/g) || []).length

    expect(minEndTimeCount).toBeGreaterThan(1)
    expect(maxStartTimeCount).toBeGreaterThan(1)
  })

  it('should include required query variables', () => {
    const query = GET_TIMELINE_CASE_DATA
    const queryString = query.loc?.source.body || ''

    expect(queryString).toContain('$minEndTime: DateTime!')
    expect(queryString).toContain('$maxStartTime: DateTime!')
    expect(queryString).toContain('$statusFilter: [RoomStatusName!]')
  })
})

describe('Query structure validation', () => {
  it('should have proper GraphQL syntax for rooms with date parameters', () => {

    const testQuery = gql`
      query TestQuery(
        $minEndTime: DateTime!
        $maxStartTime: DateTime!
        $statusFilter: [RoomStatusName!]
        $roomIds: [String!]
        $siteIds: [String!]
      ) {
        sites(siteIds: $siteIds) {
          edges {
            node {
              id
              rooms(
                roomIds: $roomIds
                statusFilter: $statusFilter
                minEndTime: $minEndTime
                maxStartTime: $maxStartTime
              ) {
                edges {
                  node {
                    id
                    name
                    status(
                      minEndTime: $minEndTime
                      maxStartTime: $maxStartTime
                    ) {
                      name
                      since
                    }
                  }
                }
              }
            }
          }
        }
      }
    `

    expect(testQuery).toBeDefined()
    expect(testQuery.kind).toBe('Document')
  })

  it('should maintain backward compatibility structure', () => {
    const backwardCompatQuery = gql`
      query BackwardCompatQuery(
        $statusFilter: [RoomStatusName!]
        $roomIds: [String!]
        $siteIds: [String!]
      ) {
        sites(siteIds: $siteIds) {
          edges {
            node {
              id
              rooms(roomIds: $roomIds, statusFilter: $statusFilter) {
                edges {
                  node {
                    id
                    name
                    status {
                      name
                      since
                    }
                  }
                }
              }
            }
          }
        }
      }
    `

    expect(backwardCompatQuery).toBeDefined()
    expect(backwardCompatQuery.kind).toBe('Document')
  })
})

describe('Date parameter consistency across queries', () => {
  it('should use consistent parameter names across all date-aware fields', () => {
    const query = GET_TIMELINE_CASE_DATA
    const queryString = query.loc?.source.body || ''
    const minEndTimeUsages = queryString.match(/minEndTime: \$\w+/g) || []
    const maxStartTimeUsages = queryString.match(/maxStartTime: \$\w+/g) || []

    minEndTimeUsages.forEach(usage => {
      expect(usage).toBe('minEndTime: $minEndTime')
    })

    maxStartTimeUsages.forEach(usage => {
      expect(usage).toBe('maxStartTime: $maxStartTime')
    })
  })

  it('should pass date parameters to all relevant resolvers', () => {
    const query = GET_TIMELINE_CASE_DATA
    const queryString = query.loc?.source.body || ''

    expect(queryString).toContain('rooms(roomIds: $roomIds, statusFilter: $statusFilter, minEndTime: $minEndTime, maxStartTime: $maxStartTime)')

    expect(queryString).toContain('status(minEndTime: $minEndTime, maxStartTime: $maxStartTime)')
    expect(queryString).toContain('apellaCases(')
    expect(queryString).toMatch(/apellaCases\([^)]*minEndTime: \$minEndTime/)
    expect(queryString).toMatch(/apellaCases\([^)]*maxStartTime: \$maxStartTime/)
    expect(queryString).toContain('turnovers(')
    expect(queryString).toMatch(/turnovers\([^)]*minEndTime: \$minEndTime/)
    expect(queryString).toMatch(/turnovers\([^)]*maxStartTime: \$maxStartTime/)
  })
})

describe('Real-world usage scenarios', () => {
  it('should support filtering closed rooms for past dates', () => {

    const testScenario = {
      variables: {
        minEndTime: '2025-04-22T00:00:00.000Z',
        maxStartTime: '2025-04-22T23:59:59.999Z',
        statusFilter: ['IDLE', 'IN_CASE', 'TURNOVER'],
        siteIds: ['test-site-id'],
        roomIds: null
      }
    }

    expect(typeof testScenario.variables.minEndTime).toBe('string')
    expect(typeof testScenario.variables.maxStartTime).toBe('string')
    expect(Array.isArray(testScenario.variables.statusFilter)).toBe(true)
  })

  it('should support filtering for specific room statuses on future dates', () => {

    const futureDate = new Date()
    futureDate.setDate(futureDate.getDate() + 30)

    const testScenario = {
      variables: {
        minEndTime: futureDate.toISOString().split('T')[0] + 'T00:00:00.000Z',
        maxStartTime: futureDate.toISOString().split('T')[0] + 'T23:59:59.999Z',
        statusFilter: ['IDLE'],
        siteIds: ['test-site-id'],
        roomIds: null
      }
    }

    expect(testScenario.variables.statusFilter).toContain('IDLE')
  })

  it('should handle timezone-aware date parameters', () => {
    const testDate = new Date('2025-04-22T10:00:00-07:00')
    const startOfDay = new Date(testDate)
    startOfDay.setHours(0, 0, 0, 0)
    const endOfDay = new Date(testDate)
    endOfDay.setHours(23, 59, 59, 999)

    const testScenario = {
      variables: {
        minEndTime: startOfDay.toISOString(),
        maxStartTime: endOfDay.toISOString(),
        statusFilter: ['IN_CASE'],
        siteIds: ['test-site-id'],
        roomIds: null
      }
    }

    expect(testScenario.variables.minEndTime).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/)
    expect(testScenario.variables.maxStartTime).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/)
  })
})
